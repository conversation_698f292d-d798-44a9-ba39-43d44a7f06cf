#!/bin/bash

# 回测框架 TLS 启动脚本

echo "🔒 启动回测框架 (TLS 模式)..."

# 检查二进制文件
if [ ! -f "./bin/backtest" ]; then
    echo "❌ 错误：找不到 backtest 二进制文件"
    exit 1
fi

# 检查 TLS 配置文件
if [ ! -f "./test_tls_config.toml" ]; then
    echo "❌ 错误：找不到 TLS 配置文件"
    exit 1
fi

# 检查证书文件
if [ ! -f "./certs/server.crt" ] || [ ! -f "./certs/server.key" ]; then
    echo "❌ 错误：找不到 TLS 证书文件"
    echo "请先运行 ./setup_tls.sh 生成证书"
    exit 1
fi

echo "✅ 使用配置文件: test_tls_config.toml"
echo "✅ TLS 证书: ./certs/"
echo ""
echo "启动 TLS 服务器..."
echo "HTTPS API: https://localhost:8083"
echo "WebSocket: wss://localhost:8082"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

./bin/backtest --config test_tls_config.toml
