#!/bin/bash

# TLS证书安装演示脚本（不需要sudo权限）
# 展示安装过程，但跳过系统级别的证书安装

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

echo -e "${BLUE}${BOLD}================================================${NC}"
echo -e "${BLUE}${BOLD}  TLS证书安装演示（无需sudo权限）${NC}"
echo -e "${BLUE}${BOLD}================================================${NC}"
echo
echo -e "${YELLOW}本演示将展示：${NC}"
echo -e "  1. 生成CA证书和服务器证书"
echo -e "  2. 创建客户端配置示例"
echo -e "  3. 验证证书有效性"
echo -e "  4. 展示客户端连接方法"
echo
echo -e "${GREEN}注意：此演示跳过系统级别证书安装（需要sudo权限）${NC}"
echo

# 1. 生成证书
echo -e "${YELLOW}1. 生成证书...${NC}"
if [[ ! -f "./certs/ca.crt" ]]; then
    ./generate_ca_certs.sh
    echo -e "${GREEN}✓ 证书生成完成${NC}"
else
    echo -e "${GREEN}✓ 证书已存在${NC}"
fi
echo

# 2. 验证证书
echo -e "${YELLOW}2. 验证证书有效性...${NC}"
if openssl x509 -in "./certs/ca.crt" -text -noout > /dev/null 2>&1; then
    echo -e "${GREEN}✓ CA证书格式正确${NC}"
else
    echo -e "${RED}❌ CA证书格式错误${NC}"
    exit 1
fi

if openssl verify -CAfile "./certs/ca.crt" "./certs/server.crt" > /dev/null 2>&1; then
    echo -e "${GREEN}✓ 证书链验证成功${NC}"
else
    echo -e "${RED}❌ 证书链验证失败${NC}"
    exit 1
fi
echo

# 3. 创建客户端配置示例
echo -e "${YELLOW}3. 创建客户端配置示例...${NC}"
./fix_tls_certificates.sh > /dev/null 2>&1 || true
echo -e "${GREEN}✓ 客户端配置示例已创建${NC}"
echo

# 4. 显示使用方法
echo -e "${YELLOW}4. 客户端连接方法...${NC}"
echo
echo -e "${BLUE}方案A: 在客户端代码中添加CA证书（推荐）${NC}"
echo -e "${GREEN}Python示例:${NC}"
echo -e "import ssl, websocket"
echo -e "ssl_context = ssl.create_default_context()"
echo -e "ssl_context.load_verify_locations('./certs/ca.crt')"
echo -e "ws = websocket.WebSocket(sslopt={'context': ssl_context})"
echo -e "ws.connect('wss://localhost:8082')"
echo
echo -e "${GREEN}Rust示例:${NC}"
echo -e "let ca_cert_data = std::fs::read('./certs/ca.crt')?;"
echo -e "let ca_cert = Certificate::from_pem(&ca_cert_data)?;"
echo -e "let connector = TlsConnector::builder()"
echo -e "    .add_root_certificate(ca_cert).build()?;"
echo
echo -e "${BLUE}方案B: 使用环境变量${NC}"
echo -e "source ./certs/setup_env.sh"
echo -e "# 然后运行你的客户端程序"
echo
echo -e "${BLUE}方案C: 系统级别安装CA证书（需要sudo）${NC}"
echo -e "sudo ./setup_tls.sh"
echo -e "# 安装后客户端无需任何配置即可连接"
echo
echo -e "${BLUE}方案D: 临时跳过证书验证（仅开发测试）${NC}"
echo -e "${GREEN}Python示例:${NC}"
echo -e "ssl_context = ssl.create_default_context()"
echo -e "ssl_context.check_hostname = False"
echo -e "ssl_context.verify_mode = ssl.CERT_NONE"
echo
echo -e "${GREEN}Rust示例:${NC}"
echo -e "let connector = TlsConnector::builder()"
echo -e "    .danger_accept_invalid_certs(true)"
echo -e "    .danger_accept_invalid_hostnames(true).build()?;"
echo

# 5. 测试连接
echo -e "${YELLOW}5. 测试TLS连接...${NC}"

# 启动测试服务器
echo -e "${BLUE}启动测试服务器...${NC}"
cargo run --bin backtest -- --config test_tls_config.toml > demo_server.log 2>&1 &
SERVER_PID=$!

# 等待服务器启动
sleep 5

if kill -0 $SERVER_PID 2>/dev/null; then
    echo -e "${GREEN}✓ TLS服务器启动成功${NC}"
    
    # 测试WSS连接
    echo -e "${BLUE}测试WSS连接...${NC}"
    if RUST_LOG=error cargo run --bin test_ca_client > /dev/null 2>&1; then
        echo -e "${GREEN}✓ WSS连接测试成功（使用CA证书验证）${NC}"
    else
        echo -e "${YELLOW}⚠ WSS连接测试失败（需要配置CA证书）${NC}"
    fi
    
    # 停止测试服务器
    kill $SERVER_PID 2>/dev/null || true
    echo -e "${GREEN}✓ 测试服务器已停止${NC}"
else
    echo -e "${RED}❌ TLS服务器启动失败${NC}"
fi

# 清理
rm -f demo_server.log

echo
echo -e "${GREEN}${BOLD}================================================${NC}"
echo -e "${GREEN}${BOLD}  演示完成！${NC}"
echo -e "${GREEN}${BOLD}================================================${NC}"
echo
echo -e "${YELLOW}总结：${NC}"
echo -e "${GREEN}✓ 证书已生成并验证${NC}"
echo -e "${GREEN}✓ 客户端配置示例已创建${NC}"
echo -e "${GREEN}✓ TLS服务器可以正常启动${NC}"
echo
echo -e "${YELLOW}下一步：${NC}"
echo -e "1. ${BLUE}选择上述任一方案配置你的客户端${NC}"
echo -e "2. ${BLUE}启动TLS服务器: cargo run --bin backtest -- --config test_tls_config.toml${NC}"
echo -e "3. ${BLUE}连接到 wss://localhost:8082 或 https://localhost:8083${NC}"
echo
echo -e "${YELLOW}如需系统级别安装（客户端无需修改）：${NC}"
echo -e "${BLUE}sudo ./setup_tls.sh${NC}"
echo
