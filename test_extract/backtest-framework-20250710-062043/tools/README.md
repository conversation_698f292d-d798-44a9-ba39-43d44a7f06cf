# Backtest Framework Testing Tools

这个目录包含用于测试回测框架的HTTP和WebSocket客户端工具。

## 安装和编译

```bash
cd tools
cargo build --release
```

## HTTP客户端工具

HTTP客户端工具用于测试框架的REST API端点。

### 基本用法

```bash
# 测试健康检查端点
cargo run --bin http_client -- -e health

# 测试配置端点
cargo run --bin http_client -- -e config

# 测试订单簿端点
cargo run --bin http_client -- -e orderbook

# 测试所有端点
cargo run --bin http_client -- -e all

# 指定自定义主机和端口
cargo run --bin http_client -- -h ************* -p 8081 -e health

# 详细输出
cargo run --bin http_client -- -e health -v
```

### POST请求示例

```bash
# 创建订单
cargo run --bin http_client -- -e orders -m POST -d '{"symbol":"BTCUSDT","side":"Buy","order_type":"Limit","quantity":1.0,"price":45000.0}'
```

### 支持的端点

- `health` - 健康检查
- `config` - 配置信息
- `orderbook` - 订单簿
- `orders` - 订单管理
- `trades` - 交易记录
- `indicators` - 技术指标
- `all` - 测试所有端点

### 命令行参数

- `-h, --host <HOST>` - 服务器主机 (默认: 127.0.0.1)
- `-p, --port <PORT>` - 服务器端口 (默认: 8081)
- `-e, --endpoint <ENDPOINT>` - API端点 (必需)
- `-m, --method <METHOD>` - HTTP方法 (默认: GET)
- `-d, --data <JSON_DATA>` - POST/PUT请求的JSON数据
- `-v, --verbose` - 详细输出

## WebSocket客户端工具

WebSocket客户端工具用于测试框架的实时数据流功能。

### 基本用法

```bash
# 测试连接
cargo run --bin websocket_client -- -m connect

# 测试订阅功能
cargo run --bin websocket_client -- -m subscribe -c "market_data,trades" -d 30

# 交互模式
cargo run --bin websocket_client -- -m interactive

# 压力测试
cargo run --bin websocket_client -- -m stress -d 60

# Ping测试
cargo run --bin websocket_client -- -m ping -d 30

# 指定自定义主机和端口
cargo run --bin websocket_client -- -h ************* -p 8080 -m connect

# 详细输出
cargo run --bin websocket_client -- -m subscribe -v
```

### 测试模式

1. **connect** - 简单连接测试
   - 连接到WebSocket服务器
   - 发送ping并等待pong
   - 测试连接延迟

2. **subscribe** - 订阅测试
   - 订阅指定频道
   - 接收实时数据
   - 统计消息接收率

3. **interactive** - 交互模式
   - 手动发送命令
   - 实时查看响应
   - 支持订阅/取消订阅

4. **stress** - 压力测试
   - 高频发送消息
   - 测试服务器处理能力
   - 统计发送/接收率

5. **ping** - 延迟测试
   - 定期发送ping
   - 测量往返延迟
   - 统计连接稳定性

### 命令行参数

- `-h, --host <HOST>` - 服务器主机 (默认: 127.0.0.1)
- `-p, --port <PORT>` - 服务器端口 (默认: 8080)
- `-m, --mode <MODE>` - 测试模式 (必需)
- `-c, --channels <CHANNELS>` - 订阅频道，逗号分隔 (默认: market_data,trades,orderbook)
- `-d, --duration <SECONDS>` - 测试持续时间，秒 (默认: 30)
- `-v, --verbose` - 详细输出

## 使用示例

### 完整测试流程

1. 启动回测框架：
```bash
cd ..
cargo run
```

2. 在另一个终端测试HTTP API：
```bash
cd tools
cargo run --bin http_client -- -e all -v
```

3. 在第三个终端测试WebSocket：
```bash
cd tools
cargo run --bin websocket_client -- -m subscribe -c "market_data,trades" -d 60 -v
```

### 性能测试

```bash
# HTTP性能测试 (需要安装wrk)
wrk -t12 -c400 -d30s http://127.0.0.1:8081/api/v1/health

# WebSocket压力测试
cargo run --bin websocket_client -- -m stress -d 120 -v
```

### 调试和故障排除

```bash
# 详细连接测试
cargo run --bin websocket_client -- -m connect -v

# 交互式调试
cargo run --bin websocket_client -- -m interactive -v
```

## 输出说明

### HTTP客户端输出

- 📊 Response Status - HTTP状态码
- ⏱️ Response Time - 响应时间
- 📋 Response Headers - 响应头 (详细模式)
- 📄 Response Body - 响应体 (JSON格式化)

### WebSocket客户端输出

- 🔗 连接状态
- 📤 发送的消息
- 📥 接收的消息
- 📊 统计信息
- 🏓 Ping/Pong延迟
- ⏱️ 持续时间和速率

## 故障排除

1. **连接被拒绝**
   - 确保回测框架正在运行
   - 检查端口是否正确
   - 确认防火墙设置

2. **编译错误**
   - 确保Rust版本 >= 1.70
   - 运行 `cargo clean` 然后重新编译

3. **WebSocket连接失败**
   - 检查WebSocket端口 (默认8080)
   - 确认服务器支持WebSocket升级

4. **HTTP请求失败**
   - 检查HTTP端口 (默认8081)
   - 验证API端点路径
   - 检查请求格式
