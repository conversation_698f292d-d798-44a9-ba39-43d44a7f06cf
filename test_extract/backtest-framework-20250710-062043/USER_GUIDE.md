# 回测框架用户指南

欢迎使用回测框架！这个压缩包包含了您开始使用所需的所有文件。

## 📁 目录结构

```
backtest-framework/
├── bin/                    # 可执行文件
│   ├── backtest           # 主程序
│   ├── http_client        # HTTP 测试客户端
│   ├── websocket_client   # WebSocket 测试客户端
│   └── test_ca_client     # TLS 测试客户端
├── data/                   # 数据目录
├── certs/                  # TLS 证书目录
├── tools/                  # 工具文档
├── *.toml                  # 配置文件
├── *.sh                    # 设置和启动脚本
├── *.md                    # 文档文件
├── start.sh               # 普通模式启动脚本
└── start_tls.sh           # TLS 模式启动脚本
```

## 🚀 快速开始

### 方法 1：使用启动脚本（推荐）

1. **普通模式（HTTP/WS）**：
   ```bash
   ./start.sh
   ```

2. **TLS 模式（HTTPS/WSS）**：
   ```bash
   # 首先设置 TLS 证书
   sudo ./setup_tls.sh

   # 然后启动 TLS 服务器
   ./start_tls.sh
   ```

### 方法 2：手动启动

1. **准备数据**：
   - 将您的市场数据文件放入 `./data/` 目录
   - 支持的格式：BookTicker、Depth、Trades CSV 文件

2. **启动服务器**：
   ```bash
   # 普通模式
   ./bin/backtest --config example_config.toml

   # TLS 模式（需要先设置证书）
   ./bin/backtest --config test_tls_config.toml
   ```

## 🔧 配置

- `example_config.toml` - 基本配置（HTTP/WS）
- `test_tls_config.toml` - TLS 配置（HTTPS/WSS）

## 🧪 测试工具

启动服务器后，您可以使用以下工具测试：

```bash
# HTTP API 测试
./bin/http_client -e health

# WebSocket 测试
./bin/websocket_client -m connect

# TLS 测试（需要先设置证书）
./bin/test_ca_client
```

## 📚 更多信息

- [快速开始指南](QUICK_START.md) - 详细的安装和使用说明
- [README](README.md) - 完整的项目文档
- [TLS 设置指南](TLS_SETUP.md) - TLS 证书配置说明
- [工具使用说明](tools/README.md) - 测试工具详细说明

## ❓ 常见问题

1. **端口被占用**：
   - 检查端口 8082 (WebSocket) 和 8083 (HTTP) 是否被占用
   - 可以在配置文件中修改端口

2. **找不到数据文件**：
   - 确保数据文件在 `./data/` 目录中
   - 检查文件格式和命名

3. **TLS 证书问题**：
   - 运行 `sudo ./setup_tls.sh` 重新生成证书
   - 查看 [TLS_SETUP.md](TLS_SETUP.md) 获取详细帮助

## 🆘 获取帮助

如果遇到问题，请：
1. 查看相关文档文件
2. 检查服务器日志输出
3. 使用测试工具验证连接

祝您使用愉快！🎉
