# 回测框架 (Backtest Framework)

一个高性能的加密货币回测框架，支持实时数据流、技术指标计算、订单匹配引擎和WebSocket/HTTP API。

## ✨ 特性

- 🚀 **高性能数据处理**：支持多种数据格式（BookTicker、Depth、Trades等）
- 📊 **技术指标计算**：内置常用技术指标（SMA、EMA、RSI、MACD等）
- 🔄 **订单匹配引擎**：完整的订单簿管理和交易撮合
- 🌐 **WebSocket/HTTP API**：实时数据推送和RESTful API
- 🔒 **TLS/SSL支持**：支持HTTPS和WSS安全连接
- 🎛️ **交互式CLI**：命令行界面控制回测流程
- ⚙️ **灵活配置**：TOML配置文件支持

## 🚀 快速开始

### 1. 克隆仓库
```bash
git clone <repository-url>
cd backtest
```

### 2. 编译项目
```bash
cargo build
```

### 3. 准备数据
确保在 `./data` 目录下有测试数据文件。

### 4. 设置TLS证书（推荐）
如果你需要使用HTTPS/WSS功能，运行一键安装脚本：
```bash
sudo ./setup_tls.sh
```

这将：
- ✅ 生成CA证书和服务器证书
- ✅ 安装CA证书到系统证书存储
- ✅ 让你的WebSocket客户端无需修改代码即可连接

### 5. 运行回测
```bash
# 普通HTTP/WS模式
cargo run --bin backtest

# 或者启用TLS的HTTPS/WSS模式
cargo run --bin backtest -- --config test_tls_config.toml
```

## 🔒 TLS/SSL 安装指南

### 新clone仓库的用户

如果你刚刚clone了这个仓库，只需要运行一个命令：

```bash
sudo ./setup_tls.sh
```

这个脚本会：
1. 生成CA证书和服务器证书
2. 安装CA证书到系统证书存储
3. 验证安装结果
4. 启动测试服务器进行验证

### 安装完成后的效果

你的WebSocket客户端可以直接连接，无需任何TLS配置：

```python
# Python
import websocket
ws = websocket.WebSocket()
ws.connect("wss://localhost:8082")  # 直接连接，无需TLS配置
```

```javascript
// Node.js
const WebSocket = require('ws');
const ws = new WebSocket('wss://localhost:8082');  // 直接连接
```

```rust
// Rust
use tokio_tungstenite::connect_async;
let (ws_stream, _) = connect_async("wss://localhost:8082").await?;  // 直接连接
```

```bash
# curl测试
curl https://localhost:8083/api/v1/health  # 无需指定CA证书
```

### 详细TLS配置

如需了解详细的TLS配置选项，请查看：[TLS_SETUP.md](TLS_SETUP.md)

## 🎛️ 使用方法

### 交互式CLI模式
```bash
cargo run --bin backtest
```

支持的命令：
- `start` - 开始回测
- `stop` - 停止回测
- `restart` - 重启回测
- `status` - 查看状态
- `logs` - 显示日志
- `help` - 显示帮助
- `exit` - 退出程序

### 配置文件模式
```bash
cargo run --bin backtest -- --config your_config.toml
```

### 显示配置
```bash
cargo run --bin backtest -- config show
```

## 📡 API 端点

### HTTP API (默认端口: 8081)
- `GET /api/v1/health` - 健康检查
- `GET /api/v1/config` - 配置信息
- `GET /api/v1/orderbook` - 订单簿
- `POST /api/v1/orders` - 创建订单
- `GET /api/v1/trades` - 交易记录
- `GET /api/v1/indicators` - 技术指标

### WebSocket API (默认端口: 8080)
```json
// 订阅数据流
{"type": "Subscribe", "subscription": "BookTicker"}

// Ping/Pong
{"type": "Ping"}
```

## 🛠️ 工具

### HTTP客户端测试工具
```bash
cd tools
cargo run --bin http_client -- -e health
```

### WebSocket客户端测试工具
```bash
cd tools
cargo run --bin websocket_client
```

## ⚙️ 配置

### 基本配置 (example_config.toml)
```toml
exchange = "Binance"
start_time = "2025-07-07T11:23:20Z"
end_time = "2025-07-07T11:25:00Z"
websocket_port = 8080
http_port = 8081
log_level = "info"

[data_paths]
root = "./data"
```

### TLS配置 (test_tls_config.toml)
```toml
# HTTP服务器TLS配置
[http_tls]
enabled = true

[http_tls.cert_source]
type = "Files"
cert_path = "./certs/server.crt"
key_path = "./certs/server.key"

# WebSocket服务器TLS配置
[websocket_tls]
enabled = true

[websocket_tls.cert_source]
type = "Files"
cert_path = "./certs/server.crt"
key_path = "./certs/server.key"
```

## 🧪 测试

### 运行所有测试
```bash
cargo test
```

### 测试TLS功能
```bash
# 测试CA证书验证
cargo run --bin test_ca_client

# 测试所有TLS解决方案
./test_all_solutions.sh
```

### 集成测试
```bash
cargo test --test bookticker_integration_test
```

## 📁 项目结构

```
├── src/
│   ├── bin/           # 可执行文件
│   ├── cli.rs         # 命令行界面
│   ├── config.rs      # 配置管理
│   ├── data/          # 数据处理
│   ├── http/          # HTTP服务器
│   ├── websocket/     # WebSocket服务器
│   ├── matching/      # 订单匹配引擎
│   ├── indicators/    # 技术指标
│   └── tls.rs         # TLS支持
├── tools/             # 客户端工具
├── certs/             # TLS证书文件
├── data/              # 测试数据
├── tests/             # 测试文件
└── work-logs/         # 开发日志
```

## 🔧 故障排除

### TLS证书问题
如果遇到证书验证错误：
```bash
# 重新安装CA证书
sudo ./setup_tls.sh

# 或查看详细指南
cat TLS_SETUP.md
```

### 端口占用
```bash
# 检查端口占用
netstat -tlnp | grep :8080
netstat -tlnp | grep :8081

# 修改配置文件中的端口
```

### 数据文件问题
确保数据文件格式正确，参考 `./data/` 目录下的示例文件。

## 📚 文档

- [TLS安装指南](TLS_SETUP.md) - 详细的TLS配置说明
- [工具使用说明](tools/README.md) - 客户端工具使用方法
- [设计文档](design-docs/overview.md) - 架构设计说明
- [开发日志](work-logs/) - 详细的开发记录

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

[MIT License](LICENSE)

---

**快速开始提示**：新用户只需运行 `sudo ./setup_tls.sh` 即可完成TLS配置，让你的WebSocket客户端无需修改即可连接！
