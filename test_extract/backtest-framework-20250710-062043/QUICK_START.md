# 🚀 快速开始指南

## 新用户一键安装

如果你刚刚clone了这个仓库，只需要运行一个命令：

```bash
sudo ./setup_tls.sh
```

**就这么简单！** 安装完成后，你的WebSocket客户端无需任何修改即可连接到TLS服务器。

## 📋 安装后的效果

### 客户端连接示例（无需TLS配置）

```python
# Python - 直接连接，无需TLS配置
import websocket
ws = websocket.WebSocket()
ws.connect("wss://localhost:8082")
```

```javascript
// Node.js - 直接连接，无需TLS配置
const WebSocket = require('ws');
const ws = new WebSocket('wss://localhost:8082');
```

```rust
// Rust - 直接连接，无需TLS配置
use tokio_tungstenite::connect_async;
let (ws_stream, _) = connect_async("wss://localhost:8082").await?;
```

```bash
# curl - 直接访问，无需指定CA证书
curl https://localhost:8083/api/v1/health
```

## 🔧 如果不想使用sudo权限

如果你不想使用sudo权限安装系统证书，可以选择以下方案：

### 方案1：在客户端代码中添加CA证书

```bash
# 生成证书
./generate_ca_certs.sh

# 在客户端代码中使用CA证书
```

**Python示例：**
```python
import ssl
import websocket

ssl_context = ssl.create_default_context()
ssl_context.load_verify_locations('./certs/ca.crt')
ws = websocket.WebSocket(sslopt={'context': ssl_context})
ws.connect('wss://localhost:8082')
```

**Rust示例：**
```rust
use native_tls::{TlsConnector, Certificate};

let ca_cert_data = std::fs::read("./certs/ca.crt")?;
let ca_cert = Certificate::from_pem(&ca_cert_data)?;
let connector = TlsConnector::builder()
    .add_root_certificate(ca_cert)
    .build()?;
```

### 方案2：使用环境变量

```bash
# 设置环境变量
source ./certs/setup_env.sh

# 然后运行你的客户端程序
```

### 方案3：跳过证书验证（仅开发测试）

**Python示例：**
```python
import ssl
ssl_context = ssl.create_default_context()
ssl_context.check_hostname = False
ssl_context.verify_mode = ssl.CERT_NONE
```

**Rust示例：**
```rust
let connector = TlsConnector::builder()
    .danger_accept_invalid_certs(true)
    .danger_accept_invalid_hostnames(true)
    .build()?;
```

## 🧪 测试安装

### 启动TLS服务器
```bash
cargo run --bin backtest -- --config test_tls_config.toml
```

### 测试连接
```bash
# 测试HTTPS
curl https://localhost:8083/api/v1/health

# 测试WSS
cargo run --bin test_ca_client

# 运行完整测试
./test_all_solutions.sh
```

## 📁 生成的文件

安装后会在 `./certs/` 目录生成：

```
./certs/
├── ca.crt                    # CA证书（客户端需要信任的根证书）
├── ca.key                    # CA私钥
├── server.crt                # 服务器证书
├── server.key                # 服务器私钥
├── ca-bundle.crt             # 证书包（包含系统证书）
├── setup_env.sh              # 环境变量设置脚本
├── rust_client_example.rs    # Rust客户端配置示例
├── python_client_example.py  # Python客户端配置示例
└── nodejs_client_example.js  # Node.js客户端配置示例
```

## 🔍 故障排除

### 问题1：权限不足
```bash
# 确保使用sudo权限
sudo ./setup_tls.sh
```

### 问题2：证书验证失败
```bash
# 重新生成和安装证书
rm -rf ./certs/
sudo ./setup_tls.sh
```

### 问题3：端口被占用
```bash
# 检查端口占用
netstat -tlnp | grep :8082
netstat -tlnp | grep :8083

# 停止占用端口的进程
sudo kill -9 <PID>
```

### 问题4：客户端仍然报证书错误

如果系统安装后客户端仍然报错，可以：

1. **重启终端/应用**：某些应用需要重启才能加载新的系统证书
2. **检查证书安装**：
   ```bash
   # Ubuntu/Debian
   ls -la /usr/local/share/ca-certificates/ | grep backtest
   
   # CentOS/RHEL
   ls -la /etc/pki/ca-trust/source/anchors/ | grep backtest
   ```
3. **使用客户端配置方案**：参考上面的方案1-3

## 📚 更多信息

- [详细TLS配置指南](TLS_SETUP.md)
- [完整README](README.md)
- [工具使用说明](tools/README.md)

## 💡 推荐流程

1. **新用户**：直接运行 `sudo ./setup_tls.sh`
2. **不想用sudo**：运行 `./demo_tls_setup.sh` 查看演示，然后选择合适的客户端配置方案
3. **遇到问题**：查看 [TLS_SETUP.md](TLS_SETUP.md) 获取详细帮助

---

**记住**：一键安装后，你的WebSocket客户端可以直接连接 `wss://localhost:8082`，无需任何TLS配置！
