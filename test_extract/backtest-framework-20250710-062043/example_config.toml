# Backtest Framework Configuration
# 这是一个可以直接运行的配置文件示例

# 交易所设置
exchange = "Binance"

# 回测时间范围
start_time = "2025-07-07T11:23:20Z"
end_time = "2025-07-07T11:25:00Z"

# 服务器端口配置
websocket_port = 8082
http_port = 8083

# 日志配置
log_level = "info"

# 性能配置
performance_target_us = 500

# HTTP服务器TLS配置
[http_tls]
# 是否启用TLS（HTTPS）
enabled = false

# TLS证书来源配置（当enabled=true时必须配置）
# 使用证书文件的配置示例：
# [http_tls.cert_source]
# type = "Files"
# cert_path = "./certs/server.crt"
# key_path = "./certs/server.key"

# 使用自签名证书的配置示例（仅用于开发测试）：
# [http_tls.cert_source]
# type = "SelfSigned"
# subject = "localhost"

# WebSocket服务器TLS配置
[websocket_tls]
# 是否启用TLS（WSS）
enabled = false

# TLS证书来源配置（当enabled=true时必须配置）
# 使用证书文件的配置示例：
# [websocket_tls.cert_source]
# type = "Files"
# cert_path = "./certs/server.crt"
# key_path = "./certs/server.key"

# 使用自签名证书的配置示例（仅用于开发测试）：
# [websocket_tls.cert_source]
# type = "SelfSigned"
# subject = "localhost"

# 数据路径配置
[data_paths]
# 数据根目录（确保此目录存在）
root = "./data"

# 可选：为不同数据类型指定专门的路径
# 注意：只有在对应目录存在时才取消注释以下配置
# bookticker = "./data/bookticker"
# depth = "./data/depth"
# orderbook = "./data/orderbook"
# trades = "./data/trades"
