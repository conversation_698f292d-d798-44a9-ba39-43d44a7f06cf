# TLS证书安装指南

本指南说明如何为回测框架设置TLS证书，使WebSocket客户端无需修改代码即可连接到启用TLS的服务器。

## 🚀 快速开始（新clone的仓库）

如果你刚刚clone了这个仓库，按照以下步骤操作：

### 1. 生成证书并安装到系统

```bash
# 1. 生成CA证书和服务器证书
./generate_ca_certs.sh

# 2. 安装CA证书到系统（需要sudo权限）
sudo ./fix_tls_certificates.sh

# 3. 启动TLS服务器
cargo run --bin backtest -- --config test_tls_config.toml
```

### 2. 验证安装

```bash
# 测试HTTPS连接（不需要指定CA证书）
curl https://localhost:8083/api/v1/health

# 测试WSS连接
cargo run --bin test_ca_client
```

如果上述命令成功执行且没有证书错误，说明系统级别CA证书安装成功！

## 📋 详细安装步骤

### 步骤1：生成证书

```bash
# 生成CA证书和服务器证书
./generate_ca_certs.sh
```

这将在 `./certs/` 目录下生成：
- `ca.crt` - CA证书（根证书）
- `ca.key` - CA私钥
- `server.crt` - 服务器证书（由CA签名）
- `server.key` - 服务器私钥

### 步骤2：安装CA证书到系统

```bash
# 运行修复脚本（需要sudo权限）
sudo ./fix_tls_certificates.sh
```

这个脚本会：
- ✅ 验证证书文件的完整性
- ✅ 将CA证书安装到系统证书存储
- ✅ 更新系统证书缓存
- ✅ 创建客户端配置示例

### 步骤3：启动TLS服务器

```bash
# 使用TLS配置启动服务器
cargo run --bin backtest -- --config test_tls_config.toml
```

服务器将启动：
- **HTTPS服务器**：`https://localhost:8083`
- **WSS服务器**：`wss://localhost:8082`

## 🔧 支持的操作系统

### Ubuntu/Debian系统
```bash
# CA证书安装位置
/usr/local/share/ca-certificates/backtest-ca.crt

# 更新证书命令
sudo update-ca-certificates
```

### CentOS/RHEL系统
```bash
# CA证书安装位置
/etc/pki/ca-trust/source/anchors/backtest-ca.crt

# 更新证书命令
sudo update-ca-trust
```

### macOS系统
```bash
# 添加到系统钥匙串
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain ./certs/ca.crt
```

## 🧪 测试验证

### 测试HTTPS连接
```bash
# 系统CA证书安装成功后，这个命令应该成功
curl https://localhost:8083/api/v1/health

# 如果失败，可以手动指定CA证书
curl --cacert ./certs/ca.crt https://localhost:8083/api/v1/health
```

### 测试WSS连接
```bash
# 使用我们的测试客户端
cargo run --bin test_ca_client

# 运行完整测试套件
./test_all_solutions.sh
```

## 📱 客户端连接示例

安装系统级别CA证书后，客户端无需特殊配置即可连接：

### Python客户端
```python
import websocket

# 无需特殊TLS配置，系统会自动使用安装的CA证书
ws = websocket.WebSocket()
ws.connect("wss://localhost:8082")
```

### Node.js客户端
```javascript
const WebSocket = require('ws');

// 无需特殊TLS配置
const ws = new WebSocket('wss://localhost:8082');
```

### Rust客户端
```rust
use tokio_tungstenite::connect_async;

// 无需特殊TLS配置
let (ws_stream, _) = connect_async("wss://localhost:8082").await?;
```

### curl测试
```bash
# 无需指定CA证书
curl https://localhost:8083/api/v1/health
```

## 🔍 故障排除

### 问题1：证书验证失败
```
TLS error: certificate verify failed: (unable to get local issuer certificate)
```

**解决方案：**
```bash
# 重新安装CA证书
sudo ./fix_tls_certificates.sh

# 验证CA证书是否正确安装
ls -la /usr/local/share/ca-certificates/ | grep backtest
```

### 问题2：权限不足
```
Permission denied
```

**解决方案：**
```bash
# 确保使用sudo权限
sudo ./fix_tls_certificates.sh
```

### 问题3：证书文件不存在
```
No such file or directory: ./certs/ca.crt
```

**解决方案：**
```bash
# 首先生成证书
./generate_ca_certs.sh

# 然后安装
sudo ./fix_tls_certificates.sh
```

### 问题4：端口被占用
```
Address already in use
```

**解决方案：**
```bash
# 检查端口占用
netstat -tlnp | grep :8082
netstat -tlnp | grep :8083

# 停止占用端口的进程
sudo kill -9 <PID>
```

## 📁 文件结构

安装完成后的文件结构：
```
./certs/
├── ca.crt                    # CA证书（根证书）
├── ca.key                    # CA私钥
├── server.crt                # 服务器证书
├── server.key                # 服务器私钥
├── ca-bundle.crt             # 证书包（包含系统证书）
├── setup_env.sh              # 环境变量设置脚本
├── rust_client_example.rs    # Rust客户端示例
├── python_client_example.py  # Python客户端示例
└── nodejs_client_example.js  # Node.js客户端示例
```

## 🔒 安全注意事项

1. **开发环境**：本指南生成的是自签名证书，仅适用于开发和测试环境
2. **生产环境**：生产环境应使用由受信任CA签发的证书
3. **私钥安全**：确保私钥文件的安全，不要提交到版本控制系统
4. **证书有效期**：生成的证书有效期为365天，到期后需要重新生成

## 🆘 获取帮助

如果遇到问题，可以：

1. **查看详细日志**：
   ```bash
   RUST_LOG=debug cargo run --bin backtest -- --config test_tls_config.toml
   ```

2. **运行诊断脚本**：
   ```bash
   ./test_all_solutions.sh
   ```

3. **检查证书有效性**：
   ```bash
   openssl x509 -in ./certs/ca.crt -text -noout
   openssl verify -CAfile ./certs/ca.crt ./certs/server.crt
   ```

4. **重新生成所有证书**：
   ```bash
   rm -rf ./certs/
   ./generate_ca_certs.sh
   sudo ./fix_tls_certificates.sh
   ```

---

**总结**：按照本指南操作后，你的WebSocket客户端无需任何代码修改即可连接到启用TLS的服务器！
