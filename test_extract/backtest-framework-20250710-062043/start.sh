#!/bin/bash

# 回测框架启动脚本

echo "🚀 启动回测框架..."

# 检查二进制文件
if [ ! -f "./bin/backtest" ]; then
    echo "❌ 错误：找不到 backtest 二进制文件"
    echo "请确保您在正确的目录中运行此脚本"
    exit 1
fi

# 检查配置文件
if [ ! -f "./example_config.toml" ]; then
    echo "❌ 错误：找不到配置文件"
    exit 1
fi

# 检查数据目录
if [ ! -d "./data" ]; then
    echo "❌ 错误：找不到数据目录"
    exit 1
fi

echo "✅ 使用配置文件: example_config.toml"
echo "✅ 数据目录: ./data"
echo ""
echo "启动服务器..."
echo "HTTP API: http://localhost:8083"
echo "WebSocket: ws://localhost:8082"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

./bin/backtest --config example_config.toml
