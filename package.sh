#!/bin/bash

# 回测框架打包脚本
# 此脚本将用户需要的所有文件打包到压缩包中，让用户解压后可以直接使用

set -e

# 打印消息函数
print_info() {
    echo "[INFO] $1"
}

print_success() {
    echo "[SUCCESS] $1"
}

print_warning() {
    echo "[WARNING] $1"
}

print_error() {
    echo "[ERROR] $1"
}

# 检查必要的工具
check_dependencies() {
    print_info "检查依赖工具..."

    if ! command -v cargo &> /dev/null; then
        print_error "cargo 未找到，请安装 Rust"
        exit 1
    fi

    if ! command -v tar &> /dev/null; then
        print_error "tar 未找到"
        exit 1
    fi

    print_success "依赖检查完成"
}

# 编译 release 版本
build_release() {
    print_info "编译 release 版本..."

    # 编译主项目
    cargo build --release
    if [ $? -ne 0 ]; then
        print_error "主项目编译失败"
        exit 1
    fi

    # 编译工具
    print_info "编译测试工具..."
    cd tools
    cargo build --release
    if [ $? -ne 0 ]; then
        print_error "工具编译失败"
        exit 1
    fi
    cd ..

    print_success "编译完成"
}

# 创建打包目录
create_package_dir() {
    local package_name="backtest-framework-$(date +%Y%m%d-%H%M%S)"
    local package_dir="./package/$package_name"

    print_info "创建打包目录: $package_dir" >&2

    # 清理并创建目录
    rm -rf ./package
    mkdir -p "$package_dir"

    echo "$package_dir"
}

# 复制必要文件
copy_files() {
    local package_dir="$1"

    print_info "复制文件到打包目录..."

    # 复制文档文件
    print_info "复制文档文件..."
    cp README.md "$package_dir/"
    cp QUICK_START.md "$package_dir/"
    cp TLS_SETUP.md "$package_dir/"

    # 复制配置文件
    print_info "复制配置文件..."
    cp example_config.toml "$package_dir/"
    cp test_tls_config.toml "$package_dir/"

    # 复制 TLS 设置脚本
    print_info "复制 TLS 设置脚本..."
    cp setup_tls.sh "$package_dir/"
    cp generate_ca_certs.sh "$package_dir/"
    cp fix_tls_certificates.sh "$package_dir/"
    cp demo_tls_setup.sh "$package_dir/"

    # 确保脚本可执行
    chmod +x "$package_dir"/*.sh

    # 复制二进制文件
    print_info "复制二进制文件..."
    mkdir -p "$package_dir/bin"

    # 主程序
    if [ -f "./target/release/backtest" ]; then
        cp "./target/release/backtest" "$package_dir/bin/"
    else
        print_warning "主程序二进制文件未找到，请先运行 cargo build --release"
    fi

    # 测试客户端
    if [ -f "./target/release/test_ca_client" ]; then
        cp "./target/release/test_ca_client" "$package_dir/bin/"
    fi

    if [ -f "./target/release/test_wss" ]; then
        cp "./target/release/test_wss" "$package_dir/bin/"
    fi

    # 工具
    if [ -f "./tools/target/release/http_client" ]; then
        cp "./tools/target/release/http_client" "$package_dir/bin/"
    fi

    if [ -f "./tools/target/release/websocket_client" ]; then
        cp "./tools/target/release/websocket_client" "$package_dir/bin/"
    fi

    # 复制工具文档
    print_info "复制工具文档..."
    mkdir -p "$package_dir/tools"
    cp tools/README.md "$package_dir/tools/"

    # 创建数据目录说明（用户需要自己准备数据）
    print_info "创建数据目录说明..."
    mkdir -p "$package_dir/data"
    cat > "$package_dir/data/README.md" << 'EOF'
# 数据目录

请将您的市场数据文件放在此目录中。

支持的数据格式：
- BookTicker CSV 文件
- Depth CSV 文件
- Trades CSV 文件

示例文件名：
- BTCUSDT-bookTicker-2024-03-30.csv
- BTCUSDT-depth-2024-03-30.csv
- BTCUSDT-trades-2024-03-30.csv

数据文件应包含适当的列标题和时间戳。

注意：数据文件需要用户自行准备，不包含在此压缩包中。
EOF

    # 创建 certs 目录（空的，用户运行 setup_tls.sh 时会生成）
    print_info "创建证书目录..."
    mkdir -p "$package_dir/certs"
    cat > "$package_dir/certs/README.md" << 'EOF'
# 证书目录

此目录用于存放 TLS 证书文件。

运行 `./setup_tls.sh` 后，此目录将包含：
- ca.crt - CA 证书
- ca.key - CA 私钥
- server.crt - 服务器证书
- server.key - 服务器私钥
- setup_env.sh - 环境变量设置脚本
- 其他客户端示例文件

如果您不需要 TLS 功能，可以使用 example_config.toml 配置文件。
EOF

    print_success "文件复制完成"
}

# 创建启动脚本
create_startup_scripts() {
    local package_dir="$1"

    print_info "创建启动脚本..."

    # 创建简单启动脚本
    cat > "$package_dir/start.sh" << 'EOF'
#!/bin/bash

# 回测框架启动脚本

echo "🚀 启动回测框架..."

# 检查二进制文件
if [ ! -f "./bin/backtest" ]; then
    echo "❌ 错误：找不到 backtest 二进制文件"
    echo "请确保您在正确的目录中运行此脚本"
    exit 1
fi

# 检查配置文件
if [ ! -f "./example_config.toml" ]; then
    echo "❌ 错误：找不到配置文件"
    exit 1
fi

# 检查数据目录
if [ ! -d "./data" ]; then
    echo "❌ 错误：找不到数据目录"
    exit 1
fi

echo "✅ 使用配置文件: example_config.toml"
echo "✅ 数据目录: ./data"
echo ""
echo "启动服务器..."
echo "HTTP API: http://localhost:8083"
echo "WebSocket: ws://localhost:8082"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

./bin/backtest --config example_config.toml
EOF

    # 创建 TLS 启动脚本
    cat > "$package_dir/start_tls.sh" << 'EOF'
#!/bin/bash

# 回测框架 TLS 启动脚本

echo "🔒 启动回测框架 (TLS 模式)..."

# 检查二进制文件
if [ ! -f "./bin/backtest" ]; then
    echo "❌ 错误：找不到 backtest 二进制文件"
    exit 1
fi

# 检查 TLS 配置文件
if [ ! -f "./test_tls_config.toml" ]; then
    echo "❌ 错误：找不到 TLS 配置文件"
    exit 1
fi

# 检查证书文件
if [ ! -f "./certs/server.crt" ] || [ ! -f "./certs/server.key" ]; then
    echo "❌ 错误：找不到 TLS 证书文件"
    echo "请先运行 ./setup_tls.sh 生成证书"
    exit 1
fi

echo "✅ 使用配置文件: test_tls_config.toml"
echo "✅ TLS 证书: ./certs/"
echo ""
echo "启动 TLS 服务器..."
echo "HTTPS API: https://localhost:8083"
echo "WebSocket: wss://localhost:8082"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

./bin/backtest --config test_tls_config.toml
EOF

    # 使脚本可执行
    chmod +x "$package_dir/start.sh"
    chmod +x "$package_dir/start_tls.sh"

    print_success "启动脚本创建完成"
}

# 创建用户指南
create_user_guide() {
    local package_dir="$1"

    print_info "创建用户指南..."

    cat > "$package_dir/USER_GUIDE.md" << 'EOF'
# 回测框架用户指南

欢迎使用回测框架！这个压缩包包含了您开始使用所需的所有文件。

## 📁 目录结构

```
backtest-framework/
├── bin/                    # 可执行文件
│   ├── backtest           # 主程序
│   ├── http_client        # HTTP 测试客户端
│   ├── websocket_client   # WebSocket 测试客户端
│   └── test_ca_client     # TLS 测试客户端
├── data/                   # 数据目录
├── certs/                  # TLS 证书目录
├── tools/                  # 工具文档
├── *.toml                  # 配置文件
├── *.sh                    # 设置和启动脚本
├── *.md                    # 文档文件
├── start.sh               # 普通模式启动脚本
└── start_tls.sh           # TLS 模式启动脚本
```

## 🚀 快速开始

### 方法 1：使用启动脚本（推荐）

1. **普通模式（HTTP/WS）**：
   ```bash
   ./start.sh
   ```

2. **TLS 模式（HTTPS/WSS）**：
   ```bash
   # 首先设置 TLS 证书
   sudo ./setup_tls.sh

   # 然后启动 TLS 服务器
   ./start_tls.sh
   ```

### 方法 2：手动启动

1. **准备数据**：
   - 将您的市场数据文件放入 `./data/` 目录
   - 支持的格式：BookTicker、Depth、Trades CSV 文件

2. **启动服务器**：
   ```bash
   # 普通模式
   ./bin/backtest --config example_config.toml

   # TLS 模式（需要先设置证书）
   ./bin/backtest --config test_tls_config.toml
   ```

## 🔧 配置

- `example_config.toml` - 基本配置（HTTP/WS）
- `test_tls_config.toml` - TLS 配置（HTTPS/WSS）

## 🧪 测试工具

启动服务器后，您可以使用以下工具测试：

```bash
# HTTP API 测试
./bin/http_client -e health

# WebSocket 测试
./bin/websocket_client -m connect

# TLS 测试（需要先设置证书）
./bin/test_ca_client
```

## 📚 更多信息

- [快速开始指南](QUICK_START.md) - 详细的安装和使用说明
- [README](README.md) - 完整的项目文档
- [TLS 设置指南](TLS_SETUP.md) - TLS 证书配置说明
- [工具使用说明](tools/README.md) - 测试工具详细说明

## ❓ 常见问题

1. **端口被占用**：
   - 检查端口 8082 (WebSocket) 和 8083 (HTTP) 是否被占用
   - 可以在配置文件中修改端口

2. **找不到数据文件**：
   - 确保数据文件在 `./data/` 目录中
   - 检查文件格式和命名

3. **TLS 证书问题**：
   - 运行 `sudo ./setup_tls.sh` 重新生成证书
   - 查看 [TLS_SETUP.md](TLS_SETUP.md) 获取详细帮助

## 🆘 获取帮助

如果遇到问题，请：
1. 查看相关文档文件
2. 检查服务器日志输出
3. 使用测试工具验证连接

祝您使用愉快！🎉
EOF

    print_success "用户指南创建完成"
}

# 创建压缩包
create_archive() {
    local package_dir="$1"
    local archive_name="$(basename "$package_dir").tar.gz"

    print_info "创建压缩包: $archive_name"

    cd ./package
    tar -czf "$archive_name" "$(basename "$package_dir")"
    cd ..

    # 移动压缩包到当前目录
    mv "./package/$archive_name" "./"

    print_success "压缩包创建完成: $archive_name"

    # 显示压缩包信息
    local size=$(du -h "$archive_name" | cut -f1)
    print_info "压缩包大小: $size"

    echo "$archive_name"
}

# 清理临时文件
cleanup() {
    print_info "清理临时文件..."
    rm -rf ./package
    print_success "清理完成"
}

# 主函数
main() {
    echo "========================================"
    echo "    回测框架打包脚本"
    echo "========================================"
    echo ""

    # 检查依赖
    check_dependencies

    # 编译 release 版本
    build_release

    # 创建打包目录
    package_dir=$(create_package_dir)

    # 复制文件
    copy_files "$package_dir"

    # 创建启动脚本
    create_startup_scripts "$package_dir"

    # 创建用户指南
    create_user_guide "$package_dir"

    # 创建压缩包
    archive_name=$(create_archive "$package_dir")

    # 清理临时文件
    cleanup

    echo ""
    echo "========================================"
    print_success "打包完成！"
    echo "========================================"
    echo ""
    print_info "压缩包文件: $archive_name"
    print_info "用户可以解压后直接使用："
    echo ""
    echo "  tar -xzf $archive_name"
    echo "  cd $(basename "$package_dir")"
    echo "  ./start.sh"
    echo ""
    print_info "或者使用 TLS 模式："
    echo ""
    echo "  sudo ./setup_tls.sh"
    echo "  ./start_tls.sh"
    echo ""
    print_success "打包脚本执行完成！"
}

# 运行主函数
main "$@"
